/**
 * Simplified WhatsApp Instance Creation Modal
 * 
 * Streamlined 3-step process for tenant admin users to create WhatsApp instances
 * with auto-configured system defaults and minimal user input requirements.
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-28
 */

'use client';

import React, { useState, useEffect } from 'react';
import { X, MessageSquare, Phone, QrCode, CheckCircle, AlertCircle, Loader2, ArrowRight, ArrowLeft } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { useTenant } from '@/contexts/tenant-context';
import { 
  validateInstanceName, 
  validatePhoneNumber, 
  getValidationErrors,
  createAutoChannelConfig 
} from '@/lib/utils/whatsapp-defaults';

// =====================================================
// TYPES AND INTERFACES
// =====================================================

interface SimplifiedWhatsAppCreationModalProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Function to close the modal */
  onClose: () => void;
  /** Callback when instance is successfully created */
  onSuccess?: (instanceId: string) => void;
  /** Whether creation is in progress */
  loading?: boolean;
}

interface FormData {
  instanceName: string;
  phoneNumber: string;
}

interface CreationStep {
  id: number;
  title: string;
  description: string;
  estimatedTime: string;
  icon: React.ComponentType<any>;
}

interface QRCodeData {
  code: string;
  base64: string;
  expiresAt: Date;
}

// =====================================================
// COMPONENT
// =====================================================

/**
 * SimplifiedWhatsAppCreationModal Component
 * 
 * @description Provides a streamlined 3-step process for creating WhatsApp instances
 * with auto-configured defaults and minimal user input.
 */
export const SimplifiedWhatsAppCreationModal: React.FC<SimplifiedWhatsAppCreationModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  loading = false
}) => {
  // =====================================================
  // HOOKS AND CONTEXT
  // =====================================================

  const { profile } = useAuth();
  const { organization } = useTenant();

  // =====================================================
  // STATE MANAGEMENT
  // =====================================================

  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>({
    instanceName: '',
    phoneNumber: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [qrCodeData, setQRCodeData] = useState<QRCodeData | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'waiting' | 'connecting' | 'connected' | 'error'>('waiting');
  const [instanceId, setInstanceId] = useState<string | null>(null);
  const [autoRefreshInterval, setAutoRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // =====================================================
  // STEP CONFIGURATION
  // =====================================================

  const steps: CreationStep[] = [
    {
      id: 1,
      title: 'Información Básica',
      description: 'Configura el nombre y número de WhatsApp',
      estimatedTime: '30 segundos',
      icon: MessageSquare
    },
    {
      id: 2,
      title: 'Autenticación QR',
      description: 'Escanea el código QR con WhatsApp',
      estimatedTime: '2-3 minutos',
      icon: QrCode
    },
    {
      id: 3,
      title: 'Activación Completa',
      description: 'Configuración automática finalizada',
      estimatedTime: '30 segundos',
      icon: CheckCircle
    }
  ];

  // =====================================================
  // EFFECTS
  // =====================================================

  /**
   * Reset form when modal opens/closes
   */
  useEffect(() => {
    if (isOpen) {
      setCurrentStep(1);
      setFormData({ instanceName: '', phoneNumber: '' });
      setErrors({});
      setQRCodeData(null);
      setConnectionStatus('waiting');
      setInstanceId(null);
    } else {
      // Clear auto-refresh interval when modal closes
      if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        setAutoRefreshInterval(null);
      }
    }
  }, [isOpen, autoRefreshInterval]);

  /**
   * Auto-refresh QR code every 30 seconds
   */
  useEffect(() => {
    if (currentStep === 2 && qrCodeData && connectionStatus === 'waiting') {
      const interval = setInterval(() => {
        refreshQRCode();
      }, 30000); // 30 seconds

      setAutoRefreshInterval(interval);

      return () => {
        clearInterval(interval);
        setAutoRefreshInterval(null);
      };
    }
  }, [currentStep, qrCodeData, connectionStatus]);

  // =====================================================
  // FORM HANDLERS
  // =====================================================

  /**
   * Handle form input changes
   */
  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field-specific error when user starts typing
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  /**
   * Validate current step
   */
  const validateCurrentStep = (): boolean => {
    if (currentStep === 1) {
      const validationErrors = getValidationErrors(formData.instanceName, formData.phoneNumber);
      setErrors(validationErrors);
      return Object.keys(validationErrors).length === 0;
    }
    return true;
  };

  /**
   * Handle next step
   */
  const handleNextStep = async () => {
    if (!validateCurrentStep()) {
      return;
    }

    if (currentStep === 1) {
      // Move to QR step and create instance
      await createInstance();
    } else if (currentStep === 2 && connectionStatus === 'connected') {
      // Move to completion step
      setCurrentStep(3);
      
      // Auto-close modal after 3 seconds and call success callback
      setTimeout(() => {
        if (onSuccess && instanceId) {
          onSuccess(instanceId);
        }
        onClose();
      }, 3000);
    }
  };

  /**
   * Handle previous step
   */
  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // =====================================================
  // API OPERATIONS
  // =====================================================

  /**
   * Create WhatsApp instance with auto-configuration
   */
  const createInstance = async () => {
    if (!organization?.id || !profile?.organization_id) {
      setErrors({ general: 'Error de organización. Por favor recarga la página.' });
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      // Create auto-configured instance
      const autoConfig = createAutoChannelConfig(
        formData.phoneNumber,
        formData.instanceName,
        organization.id
      );

      const response = await fetch('/api/channels/whatsapp/instances', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          instance_name: formData.instanceName,
          ...autoConfig
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Error al crear la instancia');
      }

      const result = await response.json();
      const newInstanceId = result.data?.instance?.id;
      
      if (!newInstanceId) {
        throw new Error('No se pudo obtener el ID de la instancia creada');
      }

      setInstanceId(newInstanceId);
      
      // Get QR code
      await fetchQRCode(newInstanceId);
      
      // Move to QR step
      setCurrentStep(2);
      
    } catch (error) {
      console.error('Error creating instance:', error);
      setErrors({ 
        general: error instanceof Error ? error.message : 'Error desconocido al crear la instancia' 
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * Fetch QR code for instance
   */
  const fetchQRCode = async (instanceId: string) => {
    try {
      const response = await fetch(`/api/channels/whatsapp/instances/${instanceId}/qrcode`);
      
      if (!response.ok) {
        throw new Error('Error al obtener código QR');
      }

      const qrData = await response.json();
      
      if (qrData.success && qrData.data?.qrcode) {
        setQRCodeData({
          code: qrData.data.qrcode.code,
          base64: qrData.data.qrcode.base64,
          expiresAt: new Date(Date.now() + 60000) // 1 minute from now
        });
      }
    } catch (error) {
      console.error('Error fetching QR code:', error);
      setErrors({ qr: 'Error al generar código QR' });
    }
  };

  /**
   * Refresh QR code
   */
  const refreshQRCode = async () => {
    if (!instanceId) return;
    
    try {
      const response = await fetch(`/api/channels/whatsapp/instances/${instanceId}/qrcode`, {
        method: 'POST'
      });
      
      if (response.ok) {
        await fetchQRCode(instanceId);
      }
    } catch (error) {
      console.error('Error refreshing QR code:', error);
    }
  };

  /**
   * Check connection status
   */
  const checkConnectionStatus = async () => {
    if (!instanceId) return;
    
    try {
      const response = await fetch(`/api/channels/whatsapp/instances/${instanceId}/status`);
      
      if (response.ok) {
        const statusData = await response.json();
        const status = statusData.data?.status;
        
        if (status === 'connected') {
          setConnectionStatus('connected');
          // Clear auto-refresh interval
          if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            setAutoRefreshInterval(null);
          }
        }
      }
    } catch (error) {
      console.error('Error checking connection status:', error);
    }
  };

  // =====================================================
  // RENDER HELPERS
  // =====================================================

  /**
   * Render step indicator
   */
  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {steps.map((step, index) => {
        const isActive = step.id === currentStep;
        const isCompleted = step.id < currentStep;
        const StepIcon = step.icon;
        
        return (
          <div key={step.id} className="flex items-center">
            <div className={`
              flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
              ${isActive ? 'border-blue-500 bg-blue-500 text-white' : 
                isCompleted ? 'border-green-500 bg-green-500 text-white' : 
                'border-gray-300 bg-white text-gray-400'}
            `}>
              {isCompleted ? (
                <CheckCircle className="h-5 w-5" />
              ) : (
                <StepIcon className="h-5 w-5" />
              )}
            </div>
            
            {index < steps.length - 1 && (
              <div className={`
                w-16 h-0.5 mx-2 transition-colors
                ${step.id < currentStep ? 'bg-green-500' : 'bg-gray-300'}
              `} />
            )}
          </div>
        );
      })}
    </div>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />
        
        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                Crear Instancia de WhatsApp
              </h3>
              <p className="text-sm text-gray-500 mt-1">
                {steps[currentStep - 1]?.description}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Step indicator */}
          {renderStepIndicator()}

          {/* Error display */}
          {errors.general && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="flex">
                <AlertCircle className="h-5 w-5 text-red-400 mr-2 flex-shrink-0 mt-0.5" />
                <p className="text-sm text-red-700">{errors.general}</p>
              </div>
            </div>
          )}

          {/* Step content */}
          <div className="mb-6">
            {currentStep === 1 && renderBasicInfoStep()}
            {currentStep === 2 && renderQRAuthStep()}
            {currentStep === 3 && renderCompletionStep()}
          </div>

          {/* Navigation buttons */}
          <div className="flex justify-between">
            <button
              type="button"
              onClick={handlePreviousStep}
              disabled={currentStep === 1 || isSubmitting}
              className={`
                px-4 py-2 text-sm font-medium rounded-md transition-colors
                ${currentStep === 1 || isSubmitting
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                }
              `}
            >
              <ArrowLeft className="h-4 w-4 mr-2 inline" />
              Anterior
            </button>

            <div className="flex space-x-3">
              <button
                type="button"
                onClick={onClose}
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              >
                Cancelar
              </button>

              {currentStep < 3 && (
                <button
                  type="button"
                  onClick={handleNextStep}
                  disabled={isSubmitting || (currentStep === 2 && connectionStatus !== 'connected')}
                  className={`
                    px-4 py-2 text-sm font-medium rounded-md transition-colors inline-flex items-center
                    ${isSubmitting || (currentStep === 2 && connectionStatus !== 'connected')
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                    }
                  `}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creando...
                    </>
                  ) : currentStep === 1 ? (
                    <>
                      Crear Instancia
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </>
                  ) : (
                    <>
                      Finalizar
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // =====================================================
  // STEP RENDER FUNCTIONS
  // =====================================================

  /**
   * Render basic information step
   */
  function renderBasicInfoStep() {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <MessageSquare className="h-12 w-12 text-blue-500 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">
            Información Básica
          </h4>
          <p className="text-sm text-gray-500">
            Solo necesitamos el nombre de la instancia y tu número de WhatsApp Business.
            Todo lo demás se configurará automáticamente.
          </p>
        </div>

        <div className="space-y-4">
          {/* Instance Name */}
          <div>
            <label htmlFor="instanceName" className="block text-sm font-medium text-gray-700 mb-1">
              Nombre de la Instancia
            </label>
            <input
              type="text"
              id="instanceName"
              value={formData.instanceName}
              onChange={(e) => handleInputChange('instanceName', e.target.value)}
              placeholder="ej: WhatsApp Consultas Médicas"
              className={`
                block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm
                ${errors.instanceName ? 'border-red-300' : 'border-gray-300'}
              `}
            />
            {errors.instanceName && (
              <p className="mt-1 text-sm text-red-600">{errors.instanceName}</p>
            )}
          </div>

          {/* Phone Number */}
          <div>
            <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
              Número de WhatsApp Business
            </label>
            <div className="relative">
              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="tel"
                id="phoneNumber"
                value={formData.phoneNumber}
                onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                placeholder="+***********"
                className={`
                  block w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm
                  ${errors.phoneNumber ? 'border-red-300' : 'border-gray-300'}
                `}
              />
            </div>
            {errors.phoneNumber && (
              <p className="mt-1 text-sm text-red-600">{errors.phoneNumber}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Formato internacional con código de país (ej: +57 para Colombia)
            </p>
          </div>
        </div>

        {/* Auto-configuration info */}
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <h5 className="text-sm font-medium text-blue-900 mb-2">
            ✨ Configuración Automática Incluida
          </h5>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• Webhook configurado automáticamente</li>
            <li>• Bot de IA para citas médicas activado</li>
            <li>• Horarios de atención predefinidos</li>
            <li>• Plantillas de mensajes médicos</li>
            <li>• Integración con sistema de citas</li>
          </ul>
        </div>
      </div>
    );
  }

  /**
   * Render QR authentication step
   */
  function renderQRAuthStep() {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <QrCode className="h-12 w-12 text-green-500 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">
            Conectar WhatsApp
          </h4>
          <p className="text-sm text-gray-500">
            Escanea el código QR con tu WhatsApp Business para conectar la instancia.
          </p>
        </div>

        {/* QR Code Display */}
        <div className="flex justify-center">
          {qrCodeData ? (
            <div className="text-center">
              <div className="bg-white p-4 rounded-lg border-2 border-gray-200 inline-block">
                <img
                  src={`data:image/png;base64,${qrCodeData.base64}`}
                  alt="QR Code"
                  className="w-48 h-48"
                />
              </div>
              <p className="text-xs text-gray-500 mt-2">
                El código se actualiza automáticamente cada 30 segundos
              </p>
            </div>
          ) : (
            <div className="flex items-center justify-center w-48 h-48 bg-gray-100 rounded-lg">
              <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
            </div>
          )}
        </div>

        {/* Connection Status */}
        <div className="text-center">
          {connectionStatus === 'waiting' && (
            <div className="flex items-center justify-center text-yellow-600">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Esperando conexión...
            </div>
          )}
          {connectionStatus === 'connecting' && (
            <div className="flex items-center justify-center text-blue-600">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Conectando...
            </div>
          )}
          {connectionStatus === 'connected' && (
            <div className="flex items-center justify-center text-green-600">
              <CheckCircle className="h-4 w-4 mr-2" />
              ¡Conectado exitosamente!
            </div>
          )}
          {connectionStatus === 'error' && (
            <div className="flex items-center justify-center text-red-600">
              <AlertCircle className="h-4 w-4 mr-2" />
              Error de conexión
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
          <h5 className="text-sm font-medium text-gray-900 mb-2">
            📱 Instrucciones
          </h5>
          <ol className="text-xs text-gray-700 space-y-1 list-decimal list-inside">
            <li>Abre WhatsApp Business en tu teléfono</li>
            <li>Ve a Configuración → Dispositivos vinculados</li>
            <li>Toca "Vincular un dispositivo"</li>
            <li>Escanea este código QR</li>
          </ol>
        </div>
      </div>
    );
  }

  /**
   * Render completion step
   */
  function renderCompletionStep() {
    return (
      <div className="space-y-6 text-center">
        <div>
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h4 className="text-xl font-medium text-gray-900 mb-2">
            ¡Instancia Creada Exitosamente!
          </h4>
          <p className="text-sm text-gray-500">
            Tu instancia de WhatsApp está lista y completamente configurada.
          </p>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <h5 className="text-sm font-medium text-green-900 mb-3">
            ✅ Configuración Completada
          </h5>
          <div className="grid grid-cols-2 gap-3 text-xs text-green-700">
            <div>• Instancia conectada</div>
            <div>• Bot de IA activado</div>
            <div>• Webhook configurado</div>
            <div>• Plantillas instaladas</div>
            <div>• Horarios definidos</div>
            <div>• Citas integradas</div>
          </div>
        </div>

        <p className="text-sm text-gray-600">
          Serás redirigido al dashboard en unos segundos...
        </p>
      </div>
    );
  }
};

export default SimplifiedWhatsAppCreationModal;

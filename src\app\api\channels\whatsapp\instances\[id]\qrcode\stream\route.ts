/**
 * WhatsApp QR Code Real-time Streaming Endpoint
 * 
 * Provides Server-Sent Events (SSE) for real-time QR code updates
 * from Evolution API webhooks.
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-28
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// =====================================================
// TYPES AND INTERFACES
// =====================================================

interface QRCodeStreamEvent {
  type: 'qr_code' | 'status_update' | 'error' | 'heartbeat';
  data: {
    instanceId: string;
    qrCode?: string;
    status?: string;
    message?: string;
    timestamp: string;
    expiresAt?: string;
  };
}

// =====================================================
// SSE STREAMING ENDPOINT
// =====================================================

/**
 * Stream QR code updates via Server-Sent Events
 * 
 * @description Provides real-time QR code updates for WhatsApp instance
 * connection. Clients can listen to this stream to receive QR codes
 * as soon as they're available from Evolution API webhooks.
 * 
 * @param request - Next.js request object
 * @param params - Route parameters containing instance ID
 * @returns SSE stream with QR code updates
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const instanceId = params.id;
  
  // Create a readable stream for SSE
  const stream = new ReadableStream({
    start(controller) {
      let isActive = true;
      let heartbeatInterval: NodeJS.Timeout;
      let pollInterval: NodeJS.Timeout;

      // SSE helper function
      const sendEvent = (event: QRCodeStreamEvent) => {
        if (!isActive) return;
        
        const data = `data: ${JSON.stringify(event)}\n\n`;
        controller.enqueue(new TextEncoder().encode(data));
      };

      // Initialize connection
      const initializeStream = async () => {
        try {
          const supabase = await createClient();

          // Authenticate user
          const { data: { user }, error: authError } = await supabase.auth.getUser();
          if (authError || !user) {
            sendEvent({
              type: 'error',
              data: {
                instanceId,
                message: 'Authentication required',
                timestamp: new Date().toISOString()
              }
            });
            controller.close();
            return;
          }

          // Get user profile and verify access
          const { data: profile } = await supabase
            .from('profiles')
            .select('organization_id, role')
            .eq('id', user.id)
            .single();

          if (!profile || !['admin', 'superadmin'].includes(profile.role)) {
            sendEvent({
              type: 'error',
              data: {
                instanceId,
                message: 'Admin access required',
                timestamp: new Date().toISOString()
              }
            });
            controller.close();
            return;
          }

          // Verify instance access
          const { data: instance } = await supabase
            .from('channel_instances')
            .select('*')
            .eq('id', instanceId)
            .eq('organization_id', profile.organization_id)
            .single();

          if (!instance) {
            sendEvent({
              type: 'error',
              data: {
                instanceId,
                message: 'Instance not found or access denied',
                timestamp: new Date().toISOString()
              }
            });
            controller.close();
            return;
          }

          // Send initial status
          sendEvent({
            type: 'status_update',
            data: {
              instanceId,
              status: instance.status,
              message: 'Connected to QR code stream',
              timestamp: new Date().toISOString()
            }
          });

          // Check for existing QR code
          const currentQR = instance.config?.whatsapp?.qr_code?.current_qr;
          const expiresAt = instance.config?.whatsapp?.qr_code?.expires_at;
          
          if (currentQR && expiresAt && new Date(expiresAt) > new Date()) {
            sendEvent({
              type: 'qr_code',
              data: {
                instanceId,
                qrCode: currentQR,
                expiresAt,
                timestamp: new Date().toISOString()
              }
            });
          }

          // Set up polling for QR code updates
          pollInterval = setInterval(async () => {
            if (!isActive) return;

            try {
              const { data: updatedInstance } = await supabase
                .from('channel_instances')
                .select('config, status')
                .eq('id', instanceId)
                .single();

              if (updatedInstance) {
                const qrCode = updatedInstance.config?.whatsapp?.qr_code?.current_qr;
                const qrExpiresAt = updatedInstance.config?.whatsapp?.qr_code?.expires_at;
                const lastUpdated = updatedInstance.config?.whatsapp?.qr_code?.last_updated;

                // Check if QR code is new or updated
                if (qrCode && qrExpiresAt && new Date(qrExpiresAt) > new Date()) {
                  sendEvent({
                    type: 'qr_code',
                    data: {
                      instanceId,
                      qrCode,
                      expiresAt: qrExpiresAt,
                      timestamp: lastUpdated || new Date().toISOString()
                    }
                  });
                }

                // Send status updates
                if (updatedInstance.status !== instance.status) {
                  sendEvent({
                    type: 'status_update',
                    data: {
                      instanceId,
                      status: updatedInstance.status,
                      timestamp: new Date().toISOString()
                    }
                  });
                }
              }
            } catch (pollError) {
              console.error('❌ Error polling for QR code updates:', pollError);
            }
          }, 2000); // Poll every 2 seconds

          // Set up heartbeat
          heartbeatInterval = setInterval(() => {
            if (!isActive) return;
            
            sendEvent({
              type: 'heartbeat',
              data: {
                instanceId,
                timestamp: new Date().toISOString()
              }
            });
          }, 30000); // Heartbeat every 30 seconds

        } catch (error) {
          console.error('❌ Error initializing QR code stream:', error);
          sendEvent({
            type: 'error',
            data: {
              instanceId,
              message: 'Failed to initialize stream',
              timestamp: new Date().toISOString()
            }
          });
          controller.close();
        }
      };

      // Handle stream cancellation
      const cleanup = () => {
        isActive = false;
        if (heartbeatInterval) clearInterval(heartbeatInterval);
        if (pollInterval) clearInterval(pollInterval);
      };

      // Initialize the stream
      initializeStream();

      // Return cleanup function
      return cleanup;
    },

    cancel() {
      console.log('🔌 QR code stream cancelled for instance:', instanceId);
    }
  });

  // Return SSE response
  return new NextResponse(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Cache-Control'
    }
  });
}
